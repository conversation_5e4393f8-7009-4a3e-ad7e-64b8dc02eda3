package cn.legendshop.product.mq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.legendshop.common.utils.StringUtils;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.legendshop.common.core.base.ResResultManager;
import cn.legendshop.common.core.config.ErpSendDataUrlConfig;
import cn.legendshop.common.core.config.ThirdParameterConfig;
import cn.legendshop.common.core.constant.HttpStatusConstants;
import cn.legendshop.common.core.enums.AccountBookEnum;
import cn.legendshop.common.core.enums.IsMroSelfEnum;
import cn.legendshop.common.core.enums.ThirdIndustrialEnum;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.model.IndustrialOuterShopConfig;
import cn.legendshop.common.rabbitmq.constants.ThridIndustrialProductMQConstant;
import cn.legendshop.common.rabbitmq.dto.ThirdPartyConstant;
import cn.legendshop.common.service.IndustrialOuterShopConfigService;
import cn.legendshop.enums.ObeiSendDataEnum;
import cn.legendshop.jd.iop.sdk.enums.JdProductStateEnum;
import cn.legendshop.jd.iop.sdk.model.response.*;
import cn.legendshop.obei.RemoteObeiClient;
import cn.legendshop.product.api.dto.*;
import cn.legendshop.product.api.dto.product.ProductBuysDTO;
import cn.legendshop.product.api.dto.product.ReturnErpProductInfoDto;
import cn.legendshop.product.api.entity.*;
import cn.legendshop.product.api.enums.*;
import cn.legendshop.product.api.vo.*;
import cn.legendshop.product.dao.*;
import cn.legendshop.product.mq.producer.IndustrialProductProducerService;
import cn.legendshop.product.mq.producer.JdProductProducerService;
import cn.legendshop.product.service.JdIopProductService;
import cn.legendshop.product.service.ObeiProductService;
import cn.legendshop.product.service.ThirdIndustrialProductService;
import cn.legendshop.product.service.ZkhProductService;
import cn.legendshop.product.service.convert.JdIopProductConverter;
import cn.legendshop.product.service.convert.ZkhProductConverter;
import cn.legendshop.product.strategy.ThirdIndustrialStrategyContext;
import cn.legendshop.search.api.dto.IndustrialProductUpdateIndexBuysDTO;
import cn.legendshop.search.api.mq.param.IndustrialProductActionParam;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.legendshop.common.data.cache.util.RedisUtil;
import com.legendshop.dao.support.EntityCriterion;
import com.legendshop.model.constant.PayMannerEnum;
import com.legendshop.model.entity.Product;
import com.legendshop.model.entity.ShopDetail;
import com.legendshop.model.entity.Sku;
import com.rabbitmq.client.Channel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.legendshop.product.api.constant.ProductServiceConstant.JD_INDUSTRIAL_IN_STORE;
import static cn.legendshop.product.api.constant.ProductServiceConstant.OBEI_INDUSTRIAL_IN_STORE;
import static cn.legendshop.product.api.enums.IndexUpdateFlagEnum.UNUPDATE;
import static cn.legendshop.product.api.enums.ThirdProductOnSaleStatusEnum.OFF_SHELF;
import static cn.legendshop.product.api.enums.ThirdProductOnSaleStatusEnum.ON_SHELF;

/**
 * 第三方工业品
 */
@Slf4j
@Component
@AllArgsConstructor
public class ThirdIndustrialProductListener {

	private final ThirdIndustrialProductService thirdIndustrialProductService;
	private final ObeiProductDao obeiProductDao;
	private final ObeiProductService obeiProductService;
	private final ThirdParameterConfig config;
	private final ProductDao productDao;
	private final SkuDao skuDao;
	private final JdProductDao jdProductDao;
	private final ObeiCommodityTaskDao obeiCommodityTaskDao;
	private final ShopDetailDao shopDetailDao;
	private final ThirdParameterConfig thirdParameterConfig;
	private final MaterialProductRelDao materialProductRelDao;
	private final JdIopProductService jdIopProductService;
	private final ZkhProductConverter zkhProductConverter;
	private final RedisUtil redisUtil;
	private final CategoryMroSupplierDao categoryMroSupplierDao;
	private final IndustrialProductProducerService industrialProductProducerService;
	private final DataSourceTransactionManager dataSourceTransactionManager;
	private final ZkhProductService zkhProductService;
	private final JdIopProductConverter jdIopProductConverter;
	private final ErpSendDataUrlConfig erpSendDataUrlConfig;
	private final JdProductHisitoryDao jdProductHisitoryDao;
	private final JdPriceModifyRecordDao jdPriceModifyRecordDao;
	private final JdProductProducerService jdProductProducerService;
	private final RedisTemplate redisTemplate;
	private final ZkhProductDao zkhProductDao;
	private final IndustrialProductDao industrialProductDao;
	private final IndustrialOuterShopConfigService industrialOuterShopConfigService;
	private final RemoteObeiClient remoteObeiClient;
	private final ThirdIndustrialStrategyContext thirdIndustrialStrategyContext;

//	@RabbitListener(bindings = @QueueBinding(
//		value = @Queue(value = ThridIndustrialProductMQConstant.OBEI_NOTICE_PRODUCT_RECEIVING_QUEUE, durable = "true"),
//		exchange = @Exchange(
//			value = ThridIndustrialProductMQConstant.OBEI_CHAIN_EXCHANGE,
//			ignoreDeclarationExceptions = "true",
//			type = ExchangeTypes.TOPIC),
//		key = {ThridIndustrialProductMQConstant.OBEI_NOTICE_UPDATE_PRODUCT_ROUTING_KEY}
//	))
//	public void noticeErpUpdateProduct(String commodityCode) {
//		log.info("欧贝商品更新通知erp" + commodityCode);
//		if (StrUtil.isEmpty(commodityCode)) {
//			log.info("不存在欧贝商品更新通知erp   commodityCode" + commodityCode);
//			return;
//		}
//		ObeiProduct obeiProduct = obeiProductDao.getByobeiProductCode(commodityCode);
//		if (ObjectUtil.isEmpty(obeiProduct)) {
//			log.info("找不到欧贝商品ObeiProduct:" + obeiProduct);
//			return;
//		}
//		//更新欧贝历史表
//		int i = obeiProductHisitoryDao.updateByProductCode(ObeiProductHisitoryStatusEnum.UPDATE.value(), commodityCode);
//		if (i == 0) {
//			log.info("已发送mq通知erp");
//			return;
//		}
//		log.info(i > 0 ? "更新历史表成功" : "更新历史表失败");
//		//主装欧贝商品dto
//		List<ReturnErpProductInfoDto> returnErpProductInfoDtos = obeiProductEntityConverter.toList(CollUtil.toList(obeiProduct));
//		//接口描述：MRO定时将商品库价格信息、库存量、上下架状态同步到ERP的商品库 url
////		String url="http://gw.nisco.cn:8083/erp/api/erp2mro/batchUpdateGoods";
//
//		String str = JSON.toJSONString(returnErpProductInfoDtos);
//
//		log.info("list 转换为json " + str);
//		JSONObject sendData = new JSONObject();
//		sendData.put("productData", str);
//		log.info("主装商品发送erp: " + JSONUtil.toJsonStr(sendData));
//		String body = HttpRequest.post(config.getErpSetting().getErpPrefix() + erpSendDataUrlConfig.getBatchUpdateGoods()).header(Header.CONTENT_TYPE, "application/json").body(JSONUtil.toJsonStr(sendData)).execute().body();
//		log.info("请求返回内容{}", body);
//		JSONObject jsonObject = JSONUtil.parseObj(body);
//		String status = (String) jsonObject.get("status");
//		if (status.equals("200")) {
//			log.info("商品更新通知erp成功！");
//		}
//	}

	/**
	 * 开启延时
	 */
//	@RabbitListener(bindings = @QueueBinding(
//		value = @Queue(value = ThridIndustrialProductMQConstant.OBEI_IN_STORE_PRODUCT_QUEUE, durable = "true"),
//		exchange = @Exchange(
//			value = ThridIndustrialProductMQConstant.OBEI_IN_STORE_EXCHANGE,
//			delayed = "true",
//			ignoreDeclarationExceptions = "true",
//			type = ExchangeTypes.TOPIC),
//		key = {ThridIndustrialProductMQConstant.OBEI_IN_STORE_PRODUCT_ROUTING_KEY}
//	))
//	public void delayReIndex(String str, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
//		log.info("商品入库检查");
//
//
//	}

//	@RabbitListener(bindings = @QueueBinding(
//		value = @Queue(value = ThridIndustrialProductMQConstant.OBEI_IN_STORE_PRODUCT_QUEUE, durable = "true"),
//		exchange = @Exchange(
//			value = ThridIndustrialProductMQConstant.OBEI_IN_STORE_EXCHANGE,
//			ignoreDeclarationExceptions = "true",
//			type = ExchangeTypes.TOPIC),
//		key = {ThridIndustrialProductMQConstant.OBEI_IN_STORE_PRODUCT_ROUTING_KEY}
//	))
//	public void obeiProductPutInStore(String str, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
//		log.info("商品入库检查");
//		industrialProductProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, "欧贝商品入库mq", JSON.toJSONString(str));
//		TransactionStatus transactionStatus = null;
//		List<String> updateSkuIds = new ArrayList<>();
//		try {
//			transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
//			List<String> obeiProdCode = new ArrayList<>();
//			List<ProductReceiveDateVo> productReceiveDateVos = JSON.parseArray(str, ProductReceiveDateVo.class);
//			log.info("Obei商品入库" + str);
//			if (CollUtil.isEmpty(productReceiveDateVos)) {
//				log.info("不存在要更新到MRO商品表的欧贝商品   commodityCode" + productReceiveDateVos);
//				return;
//			}
//			productReceiveDateVos.forEach(e -> obeiProdCode.add(e.getThirdSkuId()));
//			List<ObeiProduct> newObeiProducts = obeiProductDao.queryByobeiProductCode(obeiProdCode);
//			if (ObjectUtil.isEmpty(newObeiProducts)) {
//				log.info("找不到欧贝商品ObeiProduct" + newObeiProducts);
//				commit(transactionStatus);
//				return;
//			}
//			//处理商品解绑后重新绑定的，MRO商品表已存在的情况
//			List<Long> prodIds = newObeiProducts.stream().map(ObeiProduct::getProdId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
//
//			List<Product> productListByProdIds = this.productDao.getProductListByProdIds(prodIds);
//			log.info("从商品表读完已存在的欧贝商品信息");
//			if (CollUtil.isNotEmpty(productListByProdIds)) {
//				productListByProdIds.forEach(e -> e.setStatus(1));
//				this.productDao.update(productListByProdIds);
//				log.info("商品表已经存在此欧贝商品----结束操作");
//			}
//
//			//获取基本信息
//			ShopDetail shopdetailInfo = this.shopDetailDao.getById(thirdParameterConfig.getObeiSetting().getShopId());
//			List<ObeiProduct> obeiProducts = newObeiProducts.stream().filter(e -> e.getProdId() == null).collect(Collectors.toList());
//			if(CollUtil.isNotEmpty(obeiProducts)){
//				for (ObeiProduct obeiProduct : obeiProducts) {
//					if(obeiProduct.getProdId() == null){
//						//入库
//						obeiInStoreAction(shopdetailInfo, obeiProduct);
//					}
//				}
//				log.info("插入MRO prod和sku表信息动作结束");
//				updateSkuIds = obeiProducts.stream().map(ObeiProduct::getObeiProductCode).collect(Collectors.toList());
//			}
//			log.info("检查结束");
//			commit(transactionStatus);
//
//		} catch (Exception e) {
//			e.printStackTrace();
//			if(transactionStatus != null){
//				dataSourceTransactionManager.rollback(transactionStatus);
//			}
//		} finally {
//			//更新索引
//			if(CollUtil.isNotEmpty(updateSkuIds)){
//				for (String updateSkuId : updateSkuIds) {
//					industrialProductProducerService.updateIndustrialIndex(thirdParameterConfig.getObeiSetting().getShopId() + StrUtil.COLON + updateSkuId);
//				}
//			}
//			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//		}
//	}

	private void obeiInStoreAction(ShopDetail shopdetailInfo, ObeiProduct obeiProduct) {
		String redisKey = OBEI_INDUSTRIAL_IN_STORE + obeiProduct.getObeiProductCode();
		boolean acquiredLock = false;
		acquiredLock = this.redisTemplate.opsForValue().setIfAbsent(redisKey, 1L, 10L, TimeUnit.SECONDS);
		if (!acquiredLock) {
			return ;
		}

		DateTime dateTime = new DateTime();
		Long productId = saveObeiProductIntoMro(obeiProduct, dateTime, shopdetailInfo.getUserId());
		log.info("productId为：" + productId);
		saveSkuIntoMro(productId, obeiProduct, dateTime);

		if (acquiredLock) {
			this.redisTemplate.delete(redisKey);
		}
	}

	private void commit(TransactionStatus transactionStatus) {
		//提交事务
		dataSourceTransactionManager.commit(transactionStatus);
	}

//	@RabbitListener(bindings = @QueueBinding(
//		value = @Queue(value = ThridIndustrialProductMQConstant.JD_PRODUCT_IN_STORE_QUEUE, durable = "true"),
//		exchange = @Exchange(
//			value = ThridIndustrialProductMQConstant.JD_PRODUCT_IN_STORE_EXCHANGE,
//			ignoreDeclarationExceptions = "true",
//			type = ExchangeTypes.TOPIC),
//		key = {ThridIndustrialProductMQConstant.JD_PRODUCT_IN_STORE_ROUTING_KEY}
//	))
//	public void ProductPutInStore(String str, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
//		log.info("商品入库检查");
//		industrialProductProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, "商品入库mq", JSON.toJSONString(str));
//		TransactionStatus transactionStatus = null;
//		List<Long> updateSkuIds = new ArrayList<>();
//		try {
//			//开启事务
//			transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
//			List<String> jdProdCode = new ArrayList<>();
//			List<ProductReceiveDateVo> productReceiveDateVos = JSON.parseArray(str, ProductReceiveDateVo.class);
//			log.info("jd商品入库" + str);
//			if (CollUtil.isEmpty(productReceiveDateVos)) {
//				log.info("不存在要更新到MRO商品表的京东商品   commodityCode" + productReceiveDateVos);
//				return;
//			}
//			productReceiveDateVos.forEach(e -> jdProdCode.add(e.getThirdSkuId()));
//			List<JdProduct> newJdProducts = jdProductDao.queryByskuId(jdProdCode);
//			if (ObjectUtil.isEmpty(newJdProducts)) {
//				log.info("找不到京东商品Product" + newJdProducts);
//				commit(transactionStatus);
//				return;
//			}
//			//处理商品解绑后重新绑定的，MRO商品表已存在的情况
//			List<Long> prodIds = newJdProducts.stream().map(JdProduct::getProdId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
//
//			List<Product> productListByProdIds = this.productDao.getProductListByProdIds(prodIds);
//			log.info("从商品表读完已存在的京东商品信息");
//			if (CollUtil.isNotEmpty(productListByProdIds)) {
//				productListByProdIds.forEach(e -> e.setStatus(1));
//				this.productDao.update(productListByProdIds);
//				log.info("商品表已经存在此京东商品----结束操作");
//			}
//
//			ShopDetail shopdetailInfo = this.shopDetailDao.getById(thirdParameterConfig.getJdIOPSetting().getShopId());
//			List<JdProduct> jdProductList = newJdProducts.stream().filter(e -> e.getProdId() == null).collect(Collectors.toList());
//			if(CollUtil.isNotEmpty(jdProductList)){
//				for (JdProduct jdProduct : newJdProducts) {
//					if(jdProduct.getProdId() == null){
//						DateTime dateTime = new DateTime();
//						jdInStoreAction(shopdetailInfo, jdProduct, dateTime);
//					}
//				}
//				log.info("插入MRO prod和sku表信息动作结束");
//				updateSkuIds = jdProductList.stream().map(JdProduct::getSkuId).collect(Collectors.toList());
//			}
//			log.info("京东检查结束");
//			//提交事务
//			commit(transactionStatus);
//		} catch (Exception e) {
//			e.printStackTrace();
//			if(transactionStatus != null){
//				dataSourceTransactionManager.rollback(transactionStatus);
//			}
//		} finally {
//			if(CollUtil.isNotEmpty(updateSkuIds)){
//				for (Long updateSkuId : updateSkuIds) {
//					industrialProductProducerService.updateIndustrialIndex(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + updateSkuId);
//				}
//			}
//			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//		}
//	}

	private void jdInStoreAction(ShopDetail shopdetailInfo, JdProduct jdProduct, DateTime dateTime) {

		String redisKey = JD_INDUSTRIAL_IN_STORE + jdProduct.getSkuId();
		boolean acquiredLock = false;
		acquiredLock = this.redisTemplate.opsForValue().setIfAbsent(redisKey, 1L, 10L, TimeUnit.SECONDS);
		if (!acquiredLock) {
			return ;
		}
		Long productId = saveJdProductIntoMro(jdProduct, dateTime, shopdetailInfo);
		log.info("productId为：" + productId);
		saveJdSkuIntoMro(productId, jdProduct, dateTime);

		if (acquiredLock) {
			this.redisTemplate.delete(redisKey);
		}
	}

//	@RabbitListener(bindings = @QueueBinding(
//		value = @Queue(value = ThridIndustrialProductMQConstant.OBEI_PROD_UPDATE_QUEUE, durable = "true"),
//		exchange = @Exchange(
//			value = ThridIndustrialProductMQConstant.OBEI_OUT_STORE_EXCHANGE,
//			ignoreDeclarationExceptions = "true",
//			type = ExchangeTypes.TOPIC),
//		key = {ThridIndustrialProductMQConstant.OBEI_CHECK_KEY}
//	))
//	public void obeiPorductRemoveCheck(String commodityCode, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
//		log.info("商品入库检查");
//		industrialProductProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, "商品解绑mq", JSON.toJSONString(commodityCode));
//		try {
//			log.info("欧贝商品解绑，确定是否从商品库去除此商品 ： " + commodityCode);
//			String[] split = commodityCode.split(StrUtil.COLON);
//
//			if (StrUtil.isEmpty(split[1])) {
//				log.info("传过来的commodityCode值为空 ");
//				return;
//			}
//			List<MaterialProductRel> materialProductRels = this.materialProductRelDao.queryAllBindingStatusByProductCode(split[1], split[2]);
//			if (CollUtil.isEmpty(materialProductRels)) {
//				deleteObeiMroProductInfo(split[1]);
//			} else {
//				int size = materialProductRels.size();
//				int num = 0;
//				for (MaterialProductRel materialProductRel : materialProductRels) {
//					if (materialProductRel.getIsDeleted().equals(0) && materialProductRel.getIsSured() == 0) {
//						num++;
//					}
//				}
//				if (size == num) {
//					deleteObeiMroProductInfo(split[1]);
//				} else {
//					log.info("obei商品仍有绑定关系，不处理");
//				}
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		} finally {
//			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//		}
//	}

//	/**
//	 * 检查ERP料号做了绑定关系的商品，是否已生成PROD表记录，如果没有生成，需要补尝，如没有记录，会影响下单操作
//	 * @param commodityCode
//	 */
//	@RabbitListener(bindings = @QueueBinding(
//		value = @Queue(value = ThridIndustrialProductMQConstant.OBEI_CHECK_IN_STORE_PRODUCT_QUEUE, durable = "true"),
//		exchange = @Exchange(
//			value = ThridIndustrialProductMQConstant.OBEI_CHAIN_EXCHANGE,
//			ignoreDeclarationExceptions = "true",
//			type = ExchangeTypes.TOPIC),
//		key = {ThridIndustrialProductMQConstant.OBEI_CHECK_IN_STORE_PRODUCT_ROUTING_KEY}
//	))
//	public void checkObeiProd(String commodityCode) {
//		log.info(commodityCode);
//		List<ObeiProduct> newObeiProducts = obeiProductDao.getFailCheckInProductCode();
//
//		List<ObeiProduct> removeNullProducts = newObeiProducts.stream().filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
//
//		ShopDetail shopdetailInfo = this.shopDetailDao.getById(thirdParameterConfig.getObeiSetting().getShopId());
//
//		for (ObeiProduct obeiProduct : removeNullProducts) {
//			if(obeiProduct.getProdId() == null){
//				obeiInStoreAction(shopdetailInfo, obeiProduct);
//			}
//		}
//		log.info("插入结束" + new LocalDateTime(LocalDateTime.now()));
//	}




	private Long saveObeiProductIntoMro(ObeiProduct obeiProduct, DateTime dateTime, String userId) {

		Product product = new Product();
		product.setProdType("P");
		product.setKeyWord("obei");
		product.setVersion(1);
		product.setUserName("obei");
		product.setCategoryId(Long.parseLong(thirdParameterConfig.getObeiSetting().getMroCategoryCode()));
		product.setViews(0);
		product.setBuys(0L);
		product.setUserParameter("[]");
		product.setComments(0L);
		product.setAuditOpinion("同意");
		product.setReviewScores(100);
		product.setStockCounting(2);
		product.setStore(0);
		product.setPic(obeiProduct.getPhotoAddress());
		product.setPrice(ObjectUtil.isNotEmpty(obeiProduct.getUntaxedPrice()) ? obeiProduct.getUntaxedPrice().doubleValue() : BigDecimal.ZERO.doubleValue());
		product.setCash(obeiProduct.getPrice().doubleValue());
		product.setUnit(obeiProduct.getSaleMeasureTypeUnitChName());
		product.setStocks(0);
		product.setName(obeiProduct.getObeiProductName());
		product.setRecDate(dateTime);
		product.setModifyDate(dateTime);
		product.setStartDate(dateTime);
		product.setUserName(ThirdIndustrialEnum.Obei.getName());
		product.setUserId(userId);
		product.setShopId(thirdParameterConfig.getObeiSetting().getShopId());
		product.setShopThirdCatId(Long.getLong(obeiProduct.getClassId()));
		product.setStatus(1);
		product.setPublishStatus(obeiProduct.getStatus() == 20 ? 1 : 0);
		product.setIndustrialProdType(IndustrialProdTypeEnum.INDUSTRIAL_PROD.getType());
		product.setIsMroSelf(IsMroSelfEnum.THIRD_PROD.getType());

		log.info("开始把欧贝商品信息插入到prod表");
		Long productId = productDao.save(product);
		log.info("欧贝商品信息插入到prod表成功");
		return productId;
	}

	private void deleteObeiMroProductInfo(String obeiProductCode) {
		ObeiProduct obeiProduct = this.obeiProductDao.getByobeiProductCode(obeiProductCode);
		//删除商品表
		log.info("修改对应的状态");
		Product product = this.productDao.getProductById(obeiProduct.getProdId());
		product.setStatus(0);
		this.productDao.update(product);
	}

	private void saveSkuIntoMro(Long productId, ObeiProduct obeiProduct, DateTime dateTime) {
		Sku sku = new Sku();
		sku.setProdId(productId);
		sku.setCnProperties(obeiProduct.getTypeGauge());
		sku.setPrice(obeiProduct.getPrice().doubleValue());
		sku.setName(obeiProduct.getDisplayName());
		sku.setStocks(0L);
		if ((obeiProduct.getStock().setScale(BigDecimal.ROUND_UP).toString().length() < 12)) {
			BigDecimal bigDecimal = obeiProduct.getStock().setScale(BigDecimal.ROUND_UP);
			Long stock = bigDecimal.longValue();
			sku.setActualStocks(stock);
		} else {
			log.info("=================stock过大=============");
		}
		sku.setStatus(11);
		sku.setOuterId(obeiProduct.getObeiProductCode());
		sku.setModifyDate(dateTime);
		sku.setRecDate(dateTime);
		sku.setSkuType("P");
		sku.setTenderSku(Boolean.FALSE);
		sku.setPic(obeiProduct.getPhotoAddress());
		this.skuDao.save(sku);
		obeiProduct.setProdId(productId);
		int update = this.obeiProductDao.update(obeiProduct);
		if (update != 0) {
			log.info("欧贝商品插入成功");
		}
	}

	private void saveJdSkuIntoMro(Long productId, JdProduct jdProduct, DateTime dateTime) {
		Sku sku = new Sku();
		sku.setProdId(productId);
		sku.setCnProperties(jdProduct.getParam());
		sku.setPrice(jdProduct.getPrice().doubleValue());
		sku.setName(jdProduct.getJdProductName());
		sku.setStocks(0L);
		sku.setStatus(11);
		sku.setOuterId(jdProduct.getSkuId().toString());
		sku.setModifyDate(dateTime);
		sku.setRecDate(dateTime);
		sku.setSkuType("P");
		sku.setTenderSku(Boolean.FALSE);
		sku.setPic(jdProduct.getImagePath());
		this.skuDao.save(sku);
		jdProduct.setProdId(productId);
		int update = this.jdProductDao.update(jdProduct);
		if (update != 0) {
			log.info("京东商品插入成功");
		}
	}

	private Long saveJdProductIntoMro(JdProduct jdProduct, DateTime dateTime, ShopDetail shopDetail) {

		Product product = new Product();
		product.setProdId(jdProduct.getProdId());
		product.setProdType("P");
		product.setKeyWord("jd");
		product.setVersion(1);
		product.setUserName("jd");
		product.setCategoryId(Long.parseLong("0"));
		product.setViews(0);
		product.setBuys(0L);
		product.setUserParameter("[]");
		product.setComments(0L);
		product.setAuditOpinion("同意");
		product.setReviewScores(100);
		product.setStockCounting(2);
		product.setStore(0);
		product.setPic(jdProduct.getImagePath());
		product.setPrice(jdProduct.getJdPrice().doubleValue());
		product.setCash(jdProduct.getPrice().doubleValue());
		product.setUnit(jdProduct.getSaleUnit());
		product.setStocks(10000);
		product.setName(jdProduct.getJdProductName());
		product.setRecDate(dateTime);
		product.setModifyDate(dateTime);
		product.setStartDate(dateTime);
		product.setUserName(ThirdIndustrialEnum.JD.getName());
		product.setUserId(shopDetail.getUserId());
		product.setShopId(thirdParameterConfig.getJdIOPSetting().getShopId());
		product.setShopThirdCatId(Long.getLong(jdProduct.getCategory()));
		product.setStatus(ThirdProductOnSaleStatusEnum.findByJdStatus(jdProduct.getStatus()).getMroStatus());
		product.setPublishStatus(jdProduct.getStatus() == 10 ? 1 : 0);
		product.setExpressTransFee(0.00);
		product.setPaymentMethod(PayMannerEnum.ONLINE_PAY.value());
		product.setUpdateTime(dateTime);
		product.setIsMroSelf(IsMroSelfEnum.THIRD_PROD.getType());
		product.setIndustrialProdType(IndustrialProdTypeEnum.INDUSTRIAL_PROD.getType());

		log.info("开始把京东商品信息插入到prod表");
		Long productId = productDao.save(product);
		log.info("京东商品信息插入到prod表成功");
		return productId;
	}
//==================================================================================================================================================


	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = ThridIndustrialProductMQConstant.OBEI_NOTICE_PRODUCT_PROCESSING_QUEUE, durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.OBEI_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {ThridIndustrialProductMQConstant.OBEI_NOTICE_PRODUCT_PROCESSING_ROUTING_KEY}
	),concurrency = "3")
	public void updateObeiProduct(Long taskId) {
		ObeiCommodityTask commodityTask = obeiCommodityTaskDao.getById(taskId);
		if (ObjectUtil.isEmpty(commodityTask)) {
			log.info("消息提为空!");
			return;
		}
		if (ObeiProductProcessStatuEnum.PROCESSED.value().equals(commodityTask.getStatus())) {
			log.info("已处理完毕！导入商品库");
			return;
		}
		//真正处理商品入库
		ObeiCommodityInfoVo obeiCommodityInfoVo1 = JSONUtil.toBean(commodityTask.getCommodityTaskContent(), ObeiCommodityInfoVo.class);
		Boolean flag = obeiProductService.queryProductCallback(obeiCommodityInfoVo1);
		if (flag) {
			//更新状态为处理成功 写入数据表obei_commodity_task，20 已处理，处理次数加1
			commodityTask.setStatus(ObeiProductProcessStatuEnum.PROCESSED.value());
		} else {
			commodityTask.setStatus(ObeiProductProcessStatuEnum.PROCESSED_FAIL.value());
			//更新状态为处理失败， 失败后定时任务重新发MQ重试 写入数据表obei_commodity_task，30 处理失败，处理次数加1
		}
		log.info("进入更新欧贝商品导入");
		commodityTask.setCreateTime(DateUtil.date());
		commodityTask.setProcessCout(Optional.ofNullable(commodityTask.getProcessCout()).orElse(0L) + 1);
		obeiCommodityTaskDao.update(commodityTask);
	}

	//建立多个消费者处理统一队列中的消息
	//将该多线程消费交给Spring管理
	//然后在消息监听类中使用自定义的连接工厂
	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = ThridIndustrialProductMQConstant.JD_IN_STORE_PRODUCT_QUEUE, durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.JD_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {ThridIndustrialProductMQConstant.JD_IN_STORE_PRODUCT_ROUTING_KEY}
	), concurrency = "2")
	public void JdProductInStore(List<Long> skuIds, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
		try {
			log.info("进来京东商品入库mq");
			jdIopProductService.saveJdProduct(skuIds);
		} catch (Exception e) {
			log.error("导入京东商品mq异常", e + ":::" + skuIds);
			e.printStackTrace();
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
		}
	}

//	@RabbitListener(bindings = @QueueBinding(
//		value = @Queue(value = ThridIndustrialProductMQConstant.INDUSTRIAL_INDEX_DELETE_QUEUE, durable = "true"),
//		exchange = @Exchange(
//			value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
//			ignoreDeclarationExceptions = "true",
//			type = ExchangeTypes.TOPIC),
//		key = {ThridIndustrialProductMQConstant.INDUSTRIAL_PRODUCT_DELETE_ROUTING_KEY}
//	))
//	public void deleteIndustrial(String cat, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
//
//	}


	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = ThridIndustrialProductMQConstant.JD_PRODUCT_OUT_OFF_POOL_QUEUE, durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.JD_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {ThridIndustrialProductMQConstant.JD_PRODUCT_OUT_OFF_POOL_KEY}
	))
	public void jdproductOutOfPOol(Long skuId) {
		Long shopId = thirdParameterConfig.getJdIOPSetting().getShopId();
		IndustrialProduct jdProduct = this.industrialProductDao.getByShopIdAndSkuId(skuId + "", shopId);
		if(ObjectUtil.isNotEmpty(jdProduct)){
//			if(jdProduct.getProdId() != null){
//				Product product = this.productDao.getProductById(jdProduct.getProdId());
//				product.setStatus(ThirdProductOnSaleStatusEnum.OFF_SHELF.getMroStatus());
//				this.productDao.update(product);
//			}
			jdProduct.setState(ThirdProductOnSaleStatusEnum.OFF_SHELF.getMroStatus());
			this.industrialProductDao.updateProdByShopId(jdProduct, shopId);
			industrialProductProducerService.updateIndustrialIndex(shopId + StrUtil.COLON + jdProduct.getSkuId());
		}
	}

	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = ThridIndustrialProductMQConstant.JD_CHECK_IMPORT_NUM_QUEUE, durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {ThridIndustrialProductMQConstant.JD_CHECK_IMPORT_NUM_ROUTING_KEY}
	))
	public void jdPoolProductNumListener(String str, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
		try {
			CommodityPoolNumResponse commodityPoolNumResponse = com.alibaba.fastjson.JSONObject.parseObject(str, CommodityPoolNumResponse.class);
			log.info("开始检查商品池" + commodityPoolNumResponse.getPage_num());
			redisUtil.setObject("legendshop.JD-Iop.totalCount", 0, 2L, TimeUnit.HOURS);
			this.jdIopProductService.saveJdProductBycommodityPool(commodityPoolNumResponse, 0L, 0L);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
		}
	}

	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = ThridIndustrialProductMQConstant.JD_NOTICE_PRODUCT_RECEIVING_QUEUE, durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.JD_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {ThridIndustrialProductMQConstant.JD_NOTICE_UPDATE_PRODUCT_ROUTING_KEY}
	))
	public void noticeErpUpdateProductByJd(Long productCode, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {

		try {

			log.info("京东商品更新通知erp" + productCode);
			if (productCode == null) {
				log.info("不存在京东商品更新通知erp   productCode" + productCode);
				return;
			}
			// todo 京东商品 jdProduct不用了，后面看要改一下
			JdProduct jdProduct = jdProductDao.getBySkuId(productCode);
			if (ObjectUtil.isEmpty(jdProduct)) {
				log.info("找不到京东商品:" + JSON.toJSONString(jdProduct));
				return;
			}
			List<ReturnErpProductInfoDto> returnErpProductInfoDtos = jdIopProductConverter.toReturnErpProductInfoDtoList(CollUtil.toList(jdProduct));

			//接口描述：MRO定时将商品库价格信息、库存量、上下架状态同步到ERP的商品库 url
//		String url="http://gw.nisco.cn:8083/erp/api/erp2mro/batchUpdateGoods";

			String str = JSON.toJSONString(returnErpProductInfoDtos);

			log.info("list 转换为json " + str);
			JSONObject sendData = new JSONObject();
			sendData.put("productData", str);
			log.info("主装商品发送erp: " + JSONUtil.toJsonStr(sendData));
			String body = HttpUtil.createPost(config.getErpSetting().getErpPrefix() + erpSendDataUrlConfig.getBatchUpdateGoods()).header(Header.CONTENT_TYPE, "application/json").body(JSONUtil.toJsonStr(sendData)).execute().body();
			log.info("请求返回内容{}", body);
			JSONObject jsonObject = JSONUtil.parseObj(body);
			String status = (String) jsonObject.get("status");
			if (status.equals("200")) {
				JdPriceModifyRecord jdPriceModifyRecord = this.jdPriceModifyRecordDao.getByProperties(new EntityCriterion().eq("skuId", productCode));
				jdPriceModifyRecord.setNoticeErpStatus(1);
				this.jdPriceModifyRecordDao.update(jdPriceModifyRecord);
				log.info("商品更新通知erp成功！");
			}

		} catch (Exception e) {
			log.error("发送erp异常", e);
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
		}

	}


//	@RabbitListener(bindings = @QueueBinding(
//		value = @Queue(value = ThridIndustrialProductMQConstant.JD_CHECK_INDEX_REIMPORT_TO_MRO_QUEUE, durable = "true"),
//		exchange = @Exchange(
//			value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
//			ignoreDeclarationExceptions = "true",
//			type = ExchangeTypes.TOPIC),
//		key = {ThridIndustrialProductMQConstant.JD_CHECK_INDEX_REIMPORT_TO_ROUTING_KEY}
//	))
//	public void jdReimportIndexListener(String industrialCategoryImportParam, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
//		log.info("开始检查当前类目下的商品是不是全部导入索引");
//		try {
//			this.jdIopProductService.checkCategoryInIndex(industrialCategoryImportParam);
//		} catch (Exception e) {
//			log.error("工业类目与第三方类目绑定异常", e);
//		} finally {
//			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
//		}
//		log.info("工业类目与第三方类目绑定!商品入库结束");
//	}

	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = ThridIndustrialProductMQConstant.JD_CHECK_SALE_STATE_QUEUE, durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {ThridIndustrialProductMQConstant.JD_CHECK_SALE_STATE_ROUTING_KEY}
	))
	public void jdSaleStatsUpdate(Long skuId, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
		log.info("开始更新可售性");
		try {
			this.jdIopProductService.saleStatusCheck(skuId);
		} catch (Exception e) {
			log.error("更新可售性异常", e);
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
		}
		log.info("工业类目与第三方类目绑定!商品入库结束");
	}

	/**
	 * 消费工业品商品更新信息
	 *
	 * @param message
	 * @param channel
	 * @param tag
	 * @throws IOException
	 */
	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = ThridIndustrialProductMQConstant.PRODUCT_INDUSTRIAL_CHANGE_QUEUE, durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {ThridIndustrialProductMQConstant.PRODUCT_INDUSTRIAL_CHANGE_KEY}
	))
	public void IndustrialProductChange(IndustrialChangeDTO industrialChangeDTO, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
		try {
			if (industrialChangeDTO.getSupplierCode().equals(ThirdIndustrialEnum.JD.getSupplierCode())) {
				jdIopProductService.updateProductByType(industrialChangeDTO.getIndustrialChangeDataDTOList(), industrialChangeDTO.getType());
			}

			if (industrialChangeDTO.getSupplierCode().equals(ThirdIndustrialEnum.Zkh.getSupplierCode())) {
				zkhProductService.updateProductByType(industrialChangeDTO.getIndustrialChangeDataDTOList(), industrialChangeDTO.getType());
			}
		} catch (Exception e) {
			log.error("更改商品信息错误", e);
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
		}
	}

	/**
	 * 消费工业品商品更新信息
	 *
	 * @param message
	 * @param channel
	 * @param tag
	 * @throws IOException
	 */
	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = ThridIndustrialProductMQConstant.INDUSTRIAL_PROD_UPDATE_KILL_CACHE_QUEUE, durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {ThridIndustrialProductMQConstant.INDUSTRIAL_PROD_UPDATE_KILL_CACHE_KEY}
	))
	public void IndustrialProductChange(String indexId, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
		try {
//			productService.delLegendshopProdCache(indexId);
		} catch (Exception e) {
			log.error("更改商品信息错误", e);
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
		}
	}


	private void jdProductChange(List<IndustrialChangeDataDTO> industrialChangeDataDTOList, Integer type) {

		List<String> skuIds = industrialChangeDataDTOList.stream().map(IndustrialChangeDataDTO::getSkuId).collect(Collectors.toList());
		List<JdProduct> jdProductList = jdProductDao.queryByskuId(skuIds);
		List<Sku> skuList = skuDao.queryAllByIds(skuIds.stream().map(Long::parseLong).collect(Collectors.toList()));
		List<Long> prodIds = skuList.stream().map(Sku::getProdId).collect(Collectors.toList());
		List<Product> productList = productDao.queryAllByIds(prodIds);

		Map<String, Sku> skuMap = skuList.stream().collect(Collectors.toMap(e -> e.getSkuId().toString(), Function.identity()));
		Map<String, JdProduct> jdProductMap = jdProductList.stream().collect(Collectors.toMap(e -> e.getSkuId().toString(), Function.identity()));
		Map<Long, Product> productMap = productList.stream().collect(Collectors.toMap(Product::getProdId, Function.identity()));

		for (IndustrialChangeDataDTO industrialChangeDataDTO : industrialChangeDataDTOList) {
			JdProduct jdProduct = jdProductMap.get(industrialChangeDataDTO.getSkuId());
			Sku sku = skuMap.get(industrialChangeDataDTO.getSkuId());
			Product product = productMap.get(sku.getProdId());

			//上下架
			if (IndustrialChangeTypeEnum.UP_LOWER_SHELVES.getCode().equals(type)) {
				if (industrialChangeDataDTO.getStatus().equals(JdProductStateEnum.OFFLINE.value())) {
					jdProduct.setStatus(ThridProductStatusEnum.PROD_OFFLINE.getValue());
				}
				if (industrialChangeDataDTO.getStatus().equals(JdProductStateEnum.OFFLINE.value())) {
					jdProduct.setStatus(ThridProductStatusEnum.PROD_ONLINE.getValue());
				}
				product.setStatus(industrialChangeDataDTO.getStatus());
				sku.setStatus(industrialChangeDataDTO.getStatus());
			}

			//可售性
			if (IndustrialChangeTypeEnum.SALES_STATUS.getCode().equals(type)) {
				if (industrialChangeDataDTO.getSaleStatus().equals(JdProductStateEnum.OFFLINE.value())) {
					jdProduct.setSaleStatus(ThridProductStatusEnum.PROD_OFFLINE.getValue());
				}
				if (industrialChangeDataDTO.getSaleStatus().equals(JdProductStateEnum.OFFLINE.value())) {
					jdProduct.setSaleStatus(ThridProductStatusEnum.PROD_ONLINE.getValue());
				}
			}

			//价格
			if (IndustrialChangeTypeEnum.PRICE.getCode().equals(type)) {
				jdProduct.setPrice(industrialChangeDataDTO.getPrice());
				jdProduct.setUntaxedPrice(industrialChangeDataDTO.getUntaxedPrice());
				jdProduct.setTax(industrialChangeDataDTO.getTax());
				jdProduct.setTaxPrice(industrialChangeDataDTO.getTaxPrice());
				jdProduct.setJdPrice(industrialChangeDataDTO.getPlatformPrice());

				product.setPrice(jdProduct.getJdPrice().doubleValue());
				product.setCash(industrialChangeDataDTO.getPrice().doubleValue());
				sku.setPrice(industrialChangeDataDTO.getPrice().doubleValue());
			}

		}

		jdProductDao.update(jdProductList);
		productDao.update(productList);
		skuDao.update(skuList);

	}

//	/**
//	 * 京东商品入MRO库
//	 *
//	 * @param message
//	 * @param channel
//	 * @param tag
//	 * @throws IOException
//	 */
//	@RabbitListener(bindings = @QueueBinding(
//		value = @Queue(value = ThridIndustrialProductMQConstant.JD_IN_MRO_PRODUCT_QUEUE, durable = "true"),
//		exchange = @Exchange(
//			value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
//			ignoreDeclarationExceptions = "true",
//			type = ExchangeTypes.TOPIC),
//		key = {ThridIndustrialProductMQConstant.JD_IN_MRO_PRODUCT_KEY}
//	))
//	public void jdProductStore(Long skuId, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
//		try {
//			if(skuId == null){
//				return;
//			}
////			this.jdIopProductService.inMroStore(skuId);
//
//		} catch (Exception e) {
//			log.error("更改商品信息错误", e);
//		} finally {
//			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
//		}
//	}


//	/**
//	 * 下单报错后更新商品信息
//	 * @param message
//	 * @param channel
//	 * @param tag
//	 * @throws IOException
//	 */
//	@RabbitListener(bindings = @QueueBinding(
//		value = @Queue(value = ThridIndustrialProductMQConstant.MRO_ORDER_FAIL_PRODUCT_QUEUE, durable = "true"),
//		exchange = @Exchange(
//			value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
//			ignoreDeclarationExceptions = "true",
//			type = ExchangeTypes.TOPIC),
//		key = {ThridIndustrialProductMQConstant.MRO_ORDER_FAIL_PRODUCT_KEY}
//	))
//	public void updateProductInfo(Long prodId, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
//		try {
//			if(prodId == null){
//				return;
//			}
//			this.thirdIndustrialProductService.updateThirdProduct(prodId);
//
//		} catch (Exception e) {
//			log.error("更改商品信息错误", e);
//		} finally {
//			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
//		}
//	}

	/**
	 * 取消选品关联
	 * @param message
	 * @param channel
	 * @param tag
	 * @throws IOException
	 */
	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = ThridIndustrialProductMQConstant.INDUSTRIAL_CATEGORY_UNBIND_QUEUE, durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {ThridIndustrialProductMQConstant.INDUSTRIAL_CATEGORY_UNBIND_ROUTING_KEY}
	))
	public void updateProductInfo(String info, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
		try {
			IndustrialUnBindSupplierDTO industrialUnBindSupplierDTO = com.alibaba.fastjson.JSONObject.parseObject(info, IndustrialUnBindSupplierDTO.class);
			if (StrUtil.isEmpty(industrialUnBindSupplierDTO.getSupplier())) {
				//查询该工业品类目绑定的所有第三方类目信息
				List<CategoryMroSupplier> categoryMroSuppliers = categoryMroSupplierDao.queryByMroCategory(industrialUnBindSupplierDTO.getCategoryId());

				ExecutorService fixedThreadPool = Executors.newFixedThreadPool(2);

				CompletableFuture<Void> deleteAction = CompletableFuture.supplyAsync(() -> {

					log.info("开始删除记录");
					//删除记录
					Integer integer = categoryMroSupplierDao.unBind(industrialUnBindSupplierDTO.getCategoryId());

					return integer;
				}, fixedThreadPool).thenAccept(i -> {
					log.info("删除记录成功" + i);
					//投递消息队列 取消关联对应的第三方类目
					categoryMroSuppliers.forEach((item) -> {
//						ThridIndustrialStrategy industrialStrategy = strategyMap.get(item.getSupplier());
						IndustrialOuterShopConfig innerConfig = industrialOuterShopConfigService.getByInnerCode(item.getSupplier());
						CommonSupplierProductDTO commonSupplierProductDTO = new CommonSupplierProductDTO();
						commonSupplierProductDTO.setCategoryMroSupplierId(item.getId());
						commonSupplierProductDTO.setUpdateFlag(IndexUpdateFlagEnum.UNUPDATE.getValue());
						commonSupplierProductDTO.setMroStatus(OFF_SHELF.getMroStatus());
						log.info("1.取消关联类目更新的 类目调价id :{}",item.getId());
						//todo product rebuild 获取需要更新的工业品商品
						industrialProductDao.updateProdIndexInfoByMroBindCategoryByShopId(commonSupplierProductDTO, innerConfig.getShopId());
						//更新待更新记录标识
						jdIopProductService.deleteIndexByCategoryMroSupplierId(item.getId());
					});
				});

				log.info("解绑结束,关闭线程池");
				fixedThreadPool.shutdown();
			} else {
				//把更新表示设计为1 待定时任务更新索引
				CategoryMroSupplier categoryMroSupplier = categoryMroSupplierDao.getBySupplierId(industrialUnBindSupplierDTO.getSupplierCategoryId(), industrialUnBindSupplierDTO.getSupplier());
				IndustrialOuterShopConfig innerConfig = industrialOuterShopConfigService.getByInnerCode(categoryMroSupplier.getSupplier());
//				ThridIndustrialStrategy industrialStrategy = strategyMap.get(categoryMroSupplier.getSupplier());
				CommonSupplierProductDTO commonSupplierProductDTO = new CommonSupplierProductDTO();
				commonSupplierProductDTO.setCategoryMroSupplierId(categoryMroSupplier.getId());
				commonSupplierProductDTO.setUpdateFlag(IndexUpdateFlagEnum.UNUPDATE.getValue());
				industrialProductDao.updateProdIndexInfoByMroBindCategoryByShopId(commonSupplierProductDTO, innerConfig.getShopId());
				//取消关联
				categoryMroSupplierDao.unBind(industrialUnBindSupplierDTO);
			}
		} catch (Exception e) {
			log.error("更改商品信息错误", e);
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
		}
	}

	//==================================================================================================================================================


//	@RabbitListener(bindings = @QueueBinding(
//		value = @Queue(value = "temp.obei.fix.do_not_use", durable = "true"),
//		exchange = @Exchange(
//			value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
//			ignoreDeclarationExceptions = "true",
//			type = ExchangeTypes.TOPIC),
//		key = {"temp.obei.fix"}
//	))
//	public void fixObeiInfo(String str, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
//		try {
//			ObeiCommodityInfoVo obeiCommodityInfoVo = JSON.parseObject(str, ObeiCommodityInfoVo.class);
//			Boolean aBoolean = obeiProductService.queryProductCallback(obeiCommodityInfoVo);
//			if(aBoolean){
//				tempObeiProductDao.updateProperties(new LambdaUpdate<>(TempObeiProduct.class).set(TempObeiProduct::getStatus,"20").eq(TempObeiProduct::getCode,obeiCommodityInfoVo.getCommodityCodes().get(0)));
//			}
//		} catch (Exception e) {
//			log.info("Obei修复错误 " + e);
//		} finally {
//			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
//		}
//	}


	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = ThridIndustrialProductMQConstant.JD_UPDATE_PRODUCT_PRICE_FULL_QUEUE, durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.JD_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {ThridIndustrialProductMQConstant.JD_UPDATE_PRODUCT_PRICE_ROUTING_KEY}
	), concurrency = "3")
	public void updateJdPrice(Long skuId ,Message message, Channel channel) throws IOException {
		try {
			JdPriceModifyRecord jdPriceModifyRecord = this.jdPriceModifyRecordDao.getByProperties(new EntityCriterion().eq("skuId", skuId));
			List<PriceResponse> priceResponses = this.jdIopProductService.priceCheck(skuId.toString());
			if (CollUtil.isEmpty(priceResponses)) {
				log.info("价格查询失败");
				jdPriceModifyRecord.setStatus(1);
				jdPriceModifyRecord.setUpdateTime(new DateTime());
				this.jdPriceModifyRecordDao.update(jdPriceModifyRecord);
				return;
			}

			log.info("更新的商品skuId为" + skuId);
			Long shopId = thirdParameterConfig.getJdIOPSetting().getShopId();
			IndustrialProduct jdProduct = this.industrialProductDao.getByShopIdAndSkuId(skuId + "", shopId);
			jdPriceModifyRecord.setPriceBefore(jdProduct.getPrice());
			//为空 尝试导入商品

			for (PriceResponse priceRespons : priceResponses) {
				jdIopProductConverter.toIndustrialProductChange(priceRespons, jdProduct);
				jdPriceModifyRecord.setPriceAfter(priceRespons.getPrice());
			}
			log.info("查询完毕，更新京东商品表");

//			if (jdProduct.getProdId() != null) {
//				//更新prod表
//				Product product = this.productDao.getById(jdProduct.getProdId());
//				product.setPrice(jdProduct.getMarketPrice().doubleValue());
//				product.setCash(jdProduct.getPrice().doubleValue());
//				product.setUpdateTime(new DateTime());
//				this.productDao.update(product);
//
//				//更新sku
//				List<Sku> skus = this.skuDao.getSkuByProd(product.getProdId());
//				for (Sku sku : skus) {
//					sku.setPrice(jdProduct.getPrice().doubleValue());
//				}
//				this.skuDao.update(skus);
//
//
//			}
			log.info("发送mq通知erp更新商品");
			MaterialProductRel rel = this.materialProductRelDao.getByProperties(new EntityCriterion().eq("supplier", ThirdIndustrialEnum.JD.getCode()).eq("productCode", jdProduct.getSkuId()).eq("isSured", 1).limit(1));
			if (ObjectUtil.isNotEmpty(rel)) {
				jdProductProducerService.noticeErpProductInfoUpdate(skuId);
			}else {
				jdPriceModifyRecord.setNoticeErpStatus(2);
			}

			jdPriceModifyRecord.setStatus(2);
			jdPriceModifyRecord.setUpdateTime(new DateTime());
			this.jdPriceModifyRecordDao.update(jdPriceModifyRecord);
			this.industrialProductDao.updateProdByShopId(jdProduct, shopId);
			industrialProductProducerService.updateIndustrialIndex(shopId + StrUtil.COLON + jdProduct.getSkuId());
		} catch (Exception e) {
			log.info("JD改价错误 " + e);
			e.printStackTrace();
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
		}
	}

	private void saveHistory(Result result) {
		JdProductHisitory jdProductHisitory;
		jdProductHisitory = new JdProductHisitory();
		jdProductHisitory.setProductPool(Optional.ofNullable(result.getPage_num()).orElse("-1"));
		jdProductHisitory.setStatus(ThirdProductImportStatusEnum.UN_IN_STORE.getNum());
		jdProductHisitory.setSkuId(result.getSkuId());
		jdProductHisitory.setCreateTime(new DateTime());
		jdProductHisitory.setUpdateTime(new DateTime());
		JdProductHisitory queryResult = jdProductHisitoryDao.getByProperties(new EntityCriterion().eq("skuId", jdProductHisitory.getSkuId()).eq("productPool", jdProductHisitory.getProductPool()));
		if(ObjectUtil.isEmpty(queryResult)){
			this.jdProductHisitoryDao.save(jdProductHisitory);
		}
	}

//	@RabbitListener(bindings = @QueueBinding(
//		value = @Queue(value = ThridIndustrialProductMQConstant.TEMP_FIX_QUEUE, durable = "true"),
//		exchange = @Exchange(
//			value = ThridIndustrialProductMQConstant.JD_CHAIN_EXCHANGE,
//			ignoreDeclarationExceptions = "true",
//			type = ExchangeTypes.TOPIC),
//		key = {ThridIndustrialProductMQConstant.TEMP_FIX_ROUTING_KEY}
//	),concurrency = "2")
//	public void fixLowestBuy(Long skuId, Message message, Channel channel) throws IOException {
//
//		try {
//			productDetailResponse productDetailResponse = jdIopProductService.queryProductDetail(skuId);
//			if (ObjectUtil.isNotEmpty(productDetailResponse)) {
//				this.jdProductDao.update("update ls_jd_product set lowest_buy = ? where sku_id = ?", productDetailResponse.getLowestBuy() , skuId);
//			} else {
//				this.jdProductDao.update("update ls_jd_product set status = ? where sku_id = ?", OFF_SHELF.getThirdStatus() , skuId);
//			}
//
//
//
//			List<MaterialProductRel> materials = this.materialProductRelDao.queryByProperties(new LambdaEntityCriterion<>(MaterialProductRel.class).eq(MaterialProductRel::getProductCode, skuId).eq(MaterialProductRel::getSupplier, "2"));
//
//			if (ObjectUtil.isNotEmpty(materials)) {
//				for (MaterialProductRel material : materials) {
//					jdProductProducerService.noticeErpProductInfoUpdate(Long.parseLong(material.getProductCode()));
//				}
//			}
//
//			industrialProductProducerService.updateIndustrialIndex(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + skuId);
//		} catch (Exception e) {
//			log.info("修复错误 " + skuId + e);
//		} finally {
//			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
//		}
//
//
//	}


	/**
	 * 取消选品关联
	 * @param message
	 * @param channel
	 * @param tag
	 * @throws IOException
	 */
	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = ThridIndustrialProductMQConstant.PRODUCT_BUYS_ADD_QUEUE, durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {ThridIndustrialProductMQConstant.PRODUCT_BUYS_ADD_KEY}
	))
	public void buysAdd(BuysDTO buysDTO, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
		try {
			IndustrialProduct industrialProduct = industrialProductDao.getByIdAndShopId(buysDTO.getEntityId(), buysDTO.getShopId());
			industrialProductDao.updateBuysByShopId(buysDTO, buysDTO.getShopId());

			//更新索引数据
			IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByShopId(buysDTO.getShopId());
			IndustrialProductUpdateIndexBuysDTO industrialProductUpdateIndexBuysDTO = new IndustrialProductUpdateIndexBuysDTO();
			industrialProductUpdateIndexBuysDTO.setBuys(buysDTO.getBuys());
			industrialProductUpdateIndexBuysDTO.setIndustrialProdId(buysDTO.getEntityId());
			industrialProductUpdateIndexBuysDTO.setViews(industrialProduct.getViews().longValue());
			industrialProductUpdateIndexBuysDTO.setPlatformId(config.getPlatformId());
			industrialProductProducerService.updateProductSuggestWordScore(industrialProductUpdateIndexBuysDTO);
		} catch (Exception e) {
			log.error("购买数量+1报错", e);
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
		}
	}



	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = ThridIndustrialProductMQConstant.PROD_UPDATE_QUEUE, durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.PROD_UPDATE_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {ThridIndustrialProductMQConstant.PROD_UPDATE_BUYS_KEY}
	),concurrency = "2")
	public void changeProdBuys(List<ProductBuysDTO> productBuysDTOList,Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) {
		//todo 分表之后再支持其他表修改销量，目前只修改prod表销量,并且支持批量更新,支持增加销量
//		if(productBuysDTOList != null) {
////			List<ProductBuysDTO> productBuysDTOs = JSONArray.parseArray(productBuysDTOList,ProductBuysDTO.class);
//			for(ProductBuysDTO productBuysDTO : productBuysDTOList) {
//				SkuDto skuDTO = industrialProductService.getSkuInfoByskuIdAndShopId(productBuysDTO.getThirdSkuId(),productBuysDTO.getShopId());
//				productService.addBuys(productBuysDTO.getBuyNum(), skuDTO.getProdId());
//			}
//		}
	}

	@RabbitListener(bindings = @QueueBinding(
			value = @Queue(value = ThridIndustrialProductMQConstant.ORDER_FAIL_PRODUCT_QUEUE, durable = "true"),
			exchange = @Exchange(
					value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
					ignoreDeclarationExceptions = "true",
					type = ExchangeTypes.TOPIC),
			key = {ThridIndustrialProductMQConstant.ORDER_FAIL_PRODUCT_KEY}
	))
	public void orderFailUpdateProdInfo(Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) {
		IndustrialProduct industrialProduct = com.alibaba.fastjson.JSONObject.parseObject(message.getBody(), IndustrialProduct.class);
		if (industrialProduct == null) {
			return;
		}
		Long shopId = industrialProduct.getShopId();
		String skuId = industrialProduct.getSkuId();
		// 查询商品信息(前面的industrialProduct是从消息中获取的，只存了shopId 和 skuId，所以需要重新查询一次)
		industrialProduct = industrialProductDao.getByShopIdAndSkuId(skuId, shopId);
		if (industrialProduct == null) {
			log.info("订单失败更新商品信息失败:未查询到商品。shopId:{},skuId:{}", shopId, skuId);
			return;
		}
		thirdIndustrialStrategyContext.getStrategy(industrialProduct.getShopId()).dailyUpdateProdInfo(industrialProduct);
		log.info("订单失败更新商品信息成功:shopId:{},skuId:{}", shopId, skuId);
	}

	/**
	 * 取消选品关联
	 * @param message
	 * @param channel
	 * @param tag
	 * @throws IOException
	 */
	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = "temp.change.industrial.jd.queue", durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {"temp.change.industrial.jd"}
	),concurrency = "5")
	public void tempChangeJd(Long skuId, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
		try {

			IndustrialProduct industrialProduct1 = this.industrialProductDao.getByShopIdAndSkuId(skuId.toString(), thirdParameterConfig.getJdIOPSetting().getShopId());

			if(ObjectUtil.isNotEmpty(industrialProduct1)){
				return;
			}

			JdProduct jdProduct = this.jdProductDao.getBySkuId(skuId);

			productDetailResponse productDetailResponse = jdIopProductService.queryProductDetail(skuId);
			//部分参数转化为我们的格式
			productParamTransform(productDetailResponse);

			List<OnSaleResponse> onSaleResponses = jdIopProductService.checkOnSaleState(skuId.toString());
			OnSaleResponse onSaleResponse = onSaleResponses.stream().findFirst().get();

			List<PriceResponse> priceResponses = jdIopProductService.priceCheck(skuId.toString());
			PriceResponse priceResponse = priceResponses.stream().findFirst().get();


			IndustrialProduct industrialProduct = jdIopProductConverter.toIndustrial(productDetailResponse, null, onSaleResponse, priceResponse);

			industrialProduct.setProdId(jdProduct.getProdId());
			if(jdProduct.getProdId() != null){
				Sku sku = this.skuDao.getByOuterId(skuId.toString());
				if(ObjectUtil.isNotEmpty(sku)){
					industrialProduct.setMroSkuId(sku.getSkuId());
				}
			}

			industrialProduct.setUpdateTime(new DateTime());
			industrialProduct.setCreateTime(new DateTime());
			industrialProduct.setCategoryMroSupplierId(jdProduct.getCategoryMroSupplierId());
			industrialProduct.setMroStatus(ON_SHELF.getMroStatus());
			industrialProduct.setOriginalUrl("https://item.jd.com/" + skuId + ".html");
			industrialProduct.setShopId(thirdParameterConfig.getJdIOPSetting().getShopId());
			this.industrialProductDao.saveProdByShopId(industrialProduct, industrialProduct.getShopId());

		} catch (Exception e) {
			log.error("购买数量+1报错", e);
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
		}
	}

	private void productParamTransform(productDetailResponse productDetailResponse) {
		if(!productDetailResponse.getImagePath().contains("http://img13.360buyimg.com/n0/")){
			productDetailResponse.setImagePath("http://img13.360buyimg.com/n0/" + productDetailResponse.getImagePath());
		}

		//转换param参数
		String paramDetailJson = productDetailResponse.getParamDetailJson();
		System.out.println(JSON.toJSONString(productDetailResponse));
		String detailJson = productDetailResponse.getParamDetailJson();
		//转化为我们的格式
		if (!detailJson.equals("[]")) {
//			JdParamDetailJsonDTO
			List<CommonParam> commonParams = new ArrayList<>();
			List<JdParamDetailJsonDTO> jdParamDetailJsonDTOS = com.alibaba.fastjson.JSONObject.parseArray(detailJson, JdParamDetailJsonDTO.class);
			for (JdParamDetailJsonDTO jdParamDetailJsonDTO : jdParamDetailJsonDTOS) {
				CommonParam commonParam = new CommonParam();
				commonParam.setParamGroup(jdParamDetailJsonDTO.getGroupName());
//					System.out.println("before" + JSON.toJSONString(jdParamDetailJsonDTO));

				List<CommonParamValue> commonParamValues = new ArrayList<>();
				for (JdSpecifyDTO att : jdParamDetailJsonDTO.getAtts()) {
					String valsStr = att.getVals();
					if (StringUtils.isBlank(valsStr)) {
						continue; // 跳过空值
					}

					try {
						// 尝试作为JSON数组解析
						List<String> valStrings = JSON.parseArray(valsStr, String.class);
						String paramValue = String.join(StrUtil.SPACE, valStrings);

						CommonParamValue commonParamValue = new CommonParamValue();
						commonParamValue.setParamName(att.getAttrName());
						commonParamValue.setParamValue(paramValue);
						commonParamValues.add(commonParamValue);
					} catch (Exception e) {
						// 如果解析失败，跳过这个参数不处理
						continue;
					}
				}
				commonParam.setValue(commonParamValues);
				commonParams.add(commonParam);
//					System.out.println("after" + JSON.toJSONString(jdParamDetailJsonDTO));
			}
			productDetailResponse.setParamDetailJson(JSON.toJSONString(commonParams));
		}
	}

	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = "temp.change.industrial.obei.queue", durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {"temp.change.industrial.obei"}
	),concurrency = "10")
	public void tempChangeObei(String skuId, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
		try {

			IndustrialProduct industrialProduct1 = this.industrialProductDao.getByShopIdAndSkuId(skuId, thirdParameterConfig.getObeiSetting().getShopId());

			if(ObjectUtil.isNotEmpty(industrialProduct1)){
				return;
			}

			ObeiProduct obeiProduct = this.obeiProductDao.getByobeiProductCode(skuId);

			Map<String, Object> queryParam = Maps.newHashMap();
			queryParam.put("commodityCode", skuId);
			queryParam.put("type", "0");
			queryParam.put("companyCode", thirdParameterConfig.getObeiSetting().getCompanyCode());
			String body = remoteObeiClient.getSupplier().get(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()).get().sendPostRequest(ObeiSendDataEnum.JACQ.getCode(), queryParam);
			com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(body);
			if(jsonObject.get("message").toString().contains("未查询到商品")){
				return ;
			}


			ObeiProductVo data = jsonObject.getObject("data", ObeiProductVo.class);

			IndustrialProduct industrialProduct = buildObeiProduct(data);

			//查询面价
			Map<String, Object> queryParam2 = Maps.newHashMap();
			queryParam2.put("loginCode", thirdParameterConfig.getObeiSetting().getCompanyCode());
			queryParam2.put("commodityCode", skuId);
			R<ObeiPriceVo> obeiPrice = getObeiPrice(skuId, queryParam2);
			if(obeiPrice.getSuccess()){
				industrialProduct.setMarketPrice(obeiPrice.getResult().getFacePrice());
			}

			industrialProduct.setProdId(obeiProduct.getProdId());
			if(obeiProduct.getProdId() != null){
				Sku sku = this.skuDao.getByOuterId(skuId);
				if(ObjectUtil.isNotEmpty(sku)){
					industrialProduct.setMroSkuId(sku.getSkuId());

//					Long aLong = this.skuDao.get("SELECT SUM(actual_count) FROM ls_sub_item WHERE sku_id = ?", Long.class, sku.getId());
//					industrialProduct.setB

				}
			}

			industrialProduct.setUpdateTime(new DateTime());
			industrialProduct.setCreateTime(new DateTime());
			industrialProduct.setCategoryMroSupplierId(obeiProduct.getCategoryMroSupplierId());
			industrialProduct.setMroStatus(ON_SHELF.getMroStatus());
			industrialProduct.setOriginalUrl("https://www.obei.com.cn/obei-web-ec/OP/product-details.html?commodityCode=" + skuId);
			industrialProduct.setShopId(thirdParameterConfig.getObeiSetting().getShopId());
			this.industrialProductDao.saveProdByShopId(industrialProduct, industrialProduct.getShopId());

		} catch (Exception e) {
			log.error("购买数量+1报错", e);
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
		}
	}

	private R<ObeiPriceVo> getObeiPrice(String commodityCode, Map<String, Object> queryParam) {
		String stringQueryParam = JSON.toJSONString(queryParam);
		log.info(stringQueryParam);
		//String body = openapiClient.sendPostRequest("/jk/api/commodity/query", stringQueryParam);
		String body = remoteObeiClient.getSupplier().get(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()).get().sendPostRequest(ObeiSendDataEnum.JAQ.getCode(), stringQueryParam);
		com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(body);
		log.info("请求返回内容message{}", jsonObject.get("message"));
		ObeiPriceVo data = null;
		try {
			List<ObeiPriceVo> obeiPriceVos = JSON.parseArray((String) jsonObject.get("data"), ObeiPriceVo.class);
			data = obeiPriceVos.get(0);
		}catch (Exception e){
			e.printStackTrace();
		}

//		log.info("data数据{}" , data);
		if (ObjectUtil.isEmpty(data)) {
			log.info("data数据为空{}", data);
			return ResResultManager.setResultError("返回数据为空");
		}
		return ResResultManager.setResultSuccess(data);
	}

	/**
	 * 欧贝商品转换
	 *
	 * @param vo
	 * @return
	 */
	private IndustrialProduct buildObeiProduct(ObeiProductVo vo) {
		IndustrialProduct obeiProduct = new IndustrialProduct();
		obeiProduct.setCategory(vo.getClassCode());
		obeiProduct.setSkuId(vo.getCommodityCode());
		obeiProduct.setName(vo.getCommodityName());
		obeiProduct.setState(Integer.parseInt(vo.getStatus()) == ON_SHELF.getThirdStatus()? ON_SHELF.getMroStatus(): OFF_SHELF.getMroStatus());
		obeiProduct.setModel(vo.getTypeGauge());
//		obeiProduct.setStock(vo.getStock());
		obeiProduct.setMiniOrderQty(vo.getMiniOrderQty());
		obeiProduct.setSaleUnit(Optional.ofNullable(vo.getSaleMeasureTypeUnitChName()).orElse("件"));
		obeiProduct.setPrice(vo.getPrice());
		obeiProduct.setUntaxedPrice(vo.getUntaxedPrice());
		obeiProduct.setTaxRate(new BigDecimal(vo.getTaxRate()));
		obeiProduct.setImagePath(vo.getPhotoAddress());
		obeiProduct.setBrandName(vo.getBrandName());
		obeiProduct.setPreDeliveryDayNum(vo.getPreDeliveryDayNum());
		obeiProduct.setParam(JSON.toJSONString(vo.getCommoditySpecific()));
		obeiProduct.setWeight(Optional.ofNullable(vo.getUnitWeight()).orElse(BigDecimal.ZERO).toString());
		obeiProduct.setShopId(thirdParameterConfig.getObeiSetting().getShopId());
		obeiProduct.setCreateTime(new DateTime());
		obeiProduct.setUpdateTime(new DateTime());
		obeiProduct.setMroStatus(ON_SHELF.getMroStatus());
		obeiProduct.setUpdateFlag(UNUPDATE.getValue());
		obeiProduct.setProductType(10);
		obeiProduct.setProductArea(vo.getProduceArea());

		//ObeiCommoditySpecificationVo
		List<ObeiCommoditySpecificationVo> commoditySpecific = vo.getCommoditySpecific();
		if(CollUtil.isNotEmpty(commoditySpecific)){
			CommonParam commonParam = new CommonParam();
			commonParam.setParamGroup("商品属性");
			List<CommonParamValue> commonParamValues = new ArrayList<>();
			for (ObeiCommoditySpecificationVo obeiCommoditySpecificationVo : commoditySpecific) {
				CommonParamValue commonParamValue = new CommonParamValue();
				commonParamValue.setParamName(obeiCommoditySpecificationVo.getParamName());
				commonParamValue.setParamValue(obeiCommoditySpecificationVo.getParamValue());
				commonParamValues.add(commonParamValue);
			}
			commonParam.setValue(commonParamValues);
			obeiProduct.setParam(JSON.toJSONString(Collections.singleton(commonParam)));
		}

		//21845
		obeiProduct.setIntroduction(vo.getCommodityDesc().length() > 160000 ? vo.getCommodityDesc().substring(160000) + "..." : vo.getCommodityDesc());
		return obeiProduct;
	}

	@RabbitListener(bindings = @QueueBinding(
		value = @Queue(value = "temp.change.industrial.zkh.queue", durable = "true"),
		exchange = @Exchange(
			value = ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE,
			ignoreDeclarationExceptions = "true",
			type = ExchangeTypes.TOPIC),
		key = {"temp.change.industrial.zkh"}
	),concurrency = "10")
	public void tempChangeZkh(String skuId, Message message, Channel channel, @org.springframework.messaging.handler.annotation.Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
		try {

			IndustrialProduct industrialProduct1 = this.industrialProductDao.getByShopIdAndSkuId(skuId, thirdParameterConfig.getZkhSetting().getShopId());

			if(ObjectUtil.isNotEmpty(industrialProduct1)){
				return;
			}

			ZkhProduct zkhProduct = this.zkhProductDao.getBySkuId(skuId);

			cn.legendshop.model.response.productDetailResponse productDetailResponse = zkhProductService.queryProductDetail(skuId);
			if (ObjectUtil.isEmpty(productDetailResponse)) {
				log.error("查不到商品详情！");

				return;
			}


			List<cn.legendshop.model.response.PriceResponse> priceResponses = zkhProductService.priceCheck(Collections.singletonList(skuId));
			cn.legendshop.model.response.PriceResponse priceResponse = priceResponses.get(0);

			IndustrialProduct industrialProduct = zkhProductConverter.toIndustrial(productDetailResponse, priceResponse);
			//参数更新
			productDetailTransform(industrialProduct);

			industrialProduct.setCategoryMroSupplierId(zkhProduct.getCategoryMroSupplierId());
			industrialProduct.setMroStatus(ON_SHELF.getMroStatus());
			industrialProduct.setOriginalUrl("https://www.zkh.com/item/" + skuId + ".html");
			industrialProduct.setShopId(thirdParameterConfig.getZkhSetting().getShopId());
			this.industrialProductDao.saveProdByShopId(industrialProduct, industrialProduct.getShopId());

		} catch (Exception e) {
			log.error("购买数量+1报错", e);
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
		}
	}

	private void productDetailTransform(IndustrialProduct zkhProduct) {
		zkhProduct.setShopId(thirdParameterConfig.getZkhSetting().getShopId());
		zkhProduct.setUpdateFlag(IndexUpdateFlagEnum.UNUPDATE.getValue());
		String category = zkhProduct.getCategory();
		if (StrUtil.isNotBlank(category)) {
			List<String> categorys = com.alibaba.fastjson.JSONObject.parseArray(category, String.class);
			Collections.reverse(categorys);
			for (String termCategory : categorys) {
				if (StrUtil.isBlank(termCategory)) {
					continue;
				}
				zkhProduct.setCategory(termCategory);
				break;
			}
		}

		String param = zkhProduct.getParam();
		if (!param.equals("[]")) {
			//[{"attrSeq":"4","attrValue":"257508","attrName":"制造商型号"},{"attrSeq":"2","attrValue":"100mm","attrName":"杆长"},{"attrSeq":"1","attrValue":"3~6mm PH0-PH2","attrName":"批头规格"},{"attrSeq":"3","attrValue":"套","attrName":"销售单位"},{"attrSeq":"1","attrValue":"EVERPOWER/艾威博尔","attrName":"品牌"},{"attrSeq":"2","attrValue":"9件套多用螺丝批","attrName":"产品名称"},{"attrSeq":"5","attrValue":"6套/盒","attrName":"箱规"},{"attrSeq":"6","attrValue":"9件","attrName":"核心规格"}]
			List<ZkhParamsVo> zkhParamsVos = JSON.parseArray(param, ZkhParamsVo.class);
			CommonParam commonParam = new CommonParam();
			commonParam.setParamGroup("商品属性");
			List<CommonParamValue> commonParamValues = new ArrayList<>();
			for (ZkhParamsVo zkhParamsVo : zkhParamsVos) {
				CommonParamValue commonParamValue = new CommonParamValue();
				commonParamValue.setParamName(zkhParamsVo.getAttrName());
				commonParamValue.setParamValue(zkhParamsVo.getAttrValue());
				commonParamValue.setSeq(zkhParamsVo.getAttrSeq());
				commonParamValues.add(commonParamValue);
			}
			commonParam.setValue(commonParamValues);
			zkhProduct.setParam(JSON.toJSONString(Collections.singleton(commonParam)));
		}
	}

}
